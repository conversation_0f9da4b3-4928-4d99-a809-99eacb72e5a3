<!DOCTYPE html>
<html lang="ar">
<head>
  <meta charset="UTF-8">
  <title>لوحة تحكم الأدمن</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-6 font-sans">

<h1 class="text-2xl font-bold text-center mb-6">📦 لوحة تحكم الأدمن</h1>
<button class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded mb-4" onclick="loadOrders()">🔄 تحديث الأوردرات</button>

<div class="overflow-x-auto">
<table class="min-w-full bg-white rounded-lg shadow-md">
  <thead>
    <tr class="bg-green-600 text-white">
      <th class="py-2 px-4">اسم العميل</th>
      <th class="py-2 px-4">الهاتف</th>
      <th class="py-2 px-4">البريد</th>
      <th class="py-2 px-4">العنوان</th>
      <th class="py-2 px-4">المنتج</th>
      <th class="py-2 px-4">الكمية</th>
      <th class="py-2 px-4">التاريخ</th>
      <th class="py-2 px-4">إجراء</th>
    </tr>
  </thead>
  <tbody id="ordersTable" class="text-center">
    <tr><td colspan="8" class="py-4">جارٍ تحميل البيانات...</td></tr>
  </tbody>
</table>
</div>

<script type="module">
  import { initializeApp } from "https://www.gstatic.com/firebasejs/12.1.0/firebase-app.js";
  import { getFirestore, collection, getDocs, deleteDoc, doc } from "https://www.gstatic.com/firebasejs/12.1.0/firebase-firestore.js";

  const firebaseConfig = {
    apiKey: "AIzaSyDeFqpa8MtVF5r3kKLfYZdM3EgPZCs3zeI",
    authDomain: "zamman-admin.firebaseapp.com",
    projectId: "zamman-admin",
    storageBucket: "zamman-admin.firebasestorage.app",
    messagingSenderId: "910842307850",
    appId: "1:910842307850:web:b7548b7a3323e1a19046f3"
  };

  const app = initializeApp(firebaseConfig);
  const db = getFirestore(app);

  async function loadOrders() {
    const tbody = document.getElementById("ordersTable");
    tbody.innerHTML = "<tr><td colspan='8' class='py-4'>⏳ جاري التحميل...</td></tr>";
    try {
      const querySnapshot = await getDocs(collection(db, "orders"));
      if (querySnapshot.empty) {
        tbody.innerHTML = "<tr><td colspan='8' class='py-4'>❌ لا توجد أوردرات</td></tr>";
        return;
      }
      tbody.innerHTML = "";
      querySnapshot.forEach(docSnap => {
        const order = docSnap.data();
        tbody.innerHTML += `
          <tr class="border-b hover:bg-gray-50">
            <td class="py-2 px-4">${order.name || "-"}</td>
            <td class="py-2 px-4">${order.phone || "-"}</td>
            <td class="py-2 px-4">${order.email || "-"}</td>
            <td class="py-2 px-4">${order.address || "-"}</td>
            <td class="py-2 px-4">${order.product || "-"}</td>
            <td class="py-2 px-4">${order.quantity || "-"}</td>
            <td class="py-2 px-4">${order.date || "-"}</td>
            <td class="py-2 px-4">
              <button class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded delete-btn" data-id="${docSnap.id}">
                حذف
              </button>
            </td>
          </tr>
        `;
      });

      // تفعيل أزرار الحذف
      document.querySelectorAll(".delete-btn").forEach(btn => {
        btn.addEventListener("click", async () => {
          if (confirm("⚠ هل أنت متأكد أنك تريد حذف هذا الطلب؟")) {
            try {
              await deleteDoc(doc(db, "orders", btn.getAttribute("data-id")));
              alert("✅ تم حذف الطلب بنجاح");
              loadOrders(); // إعادة التحميل بعد الحذف
            } catch (error) {
              console.error("خطأ في الحذف:", error);
              alert("❌ حدث خطأ أثناء الحذف");
            }
          }
        });
      });

    } catch (error) {
      console.error("خطأ في جلب البيانات:", error);
      tbody.innerHTML = "<tr><td colspan='8' class='py-4'>⚠ حدث خطأ أثناء التحميل</td></tr>";
    }
  }

  loadOrders();
</script>

</body>
</html>

<!doctype html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>ساعة كربون أسود — أرقام عربية | متجر Zaman Elite</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-bx1m1+fQy0m8YvQ1I8+Yc2t2Qkfhw7W2sDq3FjGfX4s5F8j1E1Yy5a8fYf6nB0G8wz1kU3r8U3lq2jP2N+6N2g==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
</head>
<body class="bg-white text-black font-sans">

  <!-- هيدر -->
  <header class="bg-white/90 backdrop-blur border-b border-black/10 sticky top-0 z-50">
    <div class="max-w-6xl mx-auto px-4 py-4 flex items-center justify-between">
      <h1 class="text-xl font-bold tracking-tight">Zaman Elite</h1>
      
    </div>
  </header>
<style>
  *{
    font-family: 'Cairo', sans-serif;
  }
</style>
  <!-- المنتج -->
  <main class="max-w-6xl mx-auto px-4 py-12 grid md:grid-cols-2 gap-10">
    
    <!-- صور -->
    <div class="space-y-4">
      <div class="bg-white rounded-lg shadow p-4 border border-black/10">
        <img id="mainImage" src="4.jpg" alt="ساعة كربون أسود" class="w-full rounded-lg transition-transform duration-300 hover:scale-105">
      </div>
      <div class="grid grid-cols-4 gap-3">
        <img src="2.jpg" class="thumb rounded-lg border border-black/10 hover:border-black/40 cursor-pointer transition-transform hover:scale-105">
        <img src="3.jpg" class="thumb rounded-lg border border-black/10 hover:border-black/40 cursor-pointer transition-transform hover:scale-105">
        <img src="5.jpg" class="thumb rounded-lg border border-black/10 hover:border-black/40 cursor-pointer transition-transform hover:scale-105">
        <img src="1.jpg" class="thumb rounded-lg border border-black/10 hover:border-black/40 cursor-pointer transition-transform hover:scale-105">
      </div>
    </div>

    <!-- تفاصيل -->
    <div class="space-y-6">
      <h2 class="text-3xl font-extrabold">ساعة كربون أسود — أرقام عربية</h2>
      <p class="text-gray-600 leading-relaxed">
        ساعة فاخرة بتصميم كربون أسود أنيق، أرقام عربية واضحة، مقاومة للماء، مناسبة للاستخدام اليومي والمناسبات الخاصة.
      </p>

      <!-- السعر -->
      <div class="flex items-center gap-4">
        <span class="text-2xl font-extrabold">LE 400</span>
        <span class="line-through text-gray-400">LE 550</span>
      </div>

      <!-- أزرار -->
      <div class="flex gap-3">
        <button id="addToCart" class="flex-1 bg-black hover:bg-gray-800 text-white py-3 rounded-lg text-lg font-medium transition">
          <i class="fa-solid fa-cart-shopping ml-2"></i>
          أضف إلى السلة
        </button>
        <button class="px-6 py-3 border border-black rounded-lg hover:bg-gray-50 transition" aria-label="إضافة إلى المفضلة">
            <i class="fa-regular fa-heart"></i>
        </button>
      </div>


<!-- نافذة منبثقة -->
<div id="orderPopup" class="hidden fixed inset-0 bg-black/50 flex items-center justify-center z-50">
  <div class="bg-white text-black p-6 rounded-2xl w-full max-w-md shadow-2xl border border-black/10">
    <h3 class="text-2xl font-extrabold mb-4 text-center">
      <i class="fa-solid fa-cart-shopping ml-2"></i>
      إتمام الطلب
    </h3>

    <!-- رسالة الحالة -->
    <div id="statusMsg" class="mb-3 text-center text-sm font-medium text-gray-600" aria-live="polite"></div>

    <!-- بيانات العميل -->
    <div class="relative mb-3">
      <input id="name" type="text" placeholder="الاسم" class="w-full p-3 border border-black/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-black placeholder-gray-400 pl-10">
      <i class="fa-regular fa-user absolute left-3 top-3 text-gray-400" aria-hidden="true"></i>
    </div>

    <div class="flex mb-3 relative">
      <span class="px-3 flex items-center bg-gray-100 border border-black/20 border-r-0 rounded-l-lg">+20</span>
      <input id="phone" type="tel" maxlength="11" placeholder="رقم الهاتف" class="flex-1 p-3 border border-black/20 rounded-r-lg focus:outline-none focus:ring-2 focus:ring-black placeholder-gray-400 pl-10" aria-describedby="phoneHelp">
      <i class="fa-solid fa-phone absolute left-10 top-3 text-gray-400" aria-hidden="true"></i>
    </div>

    <div class="relative mb-3">
      <input id="email" type="email" placeholder="البريد الإلكتروني" class="w-full p-3 border border-black/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-black placeholder-gray-400 pl-10">
      <i class="fa-regular fa-envelope absolute left-3 top-3 text-gray-400" aria-hidden="true"></i>
    </div>

    <div class="relative mb-3">
      <input id="address" type="text" placeholder="العنوان" class="w-full p-3 border border-black/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-black placeholder-gray-400 pl-10">
      <i class="fa-solid fa-location-dot absolute left-3 top-3 text-gray-400" aria-hidden="true"></i>
    </div>

    <div class="relative mb-3">
      <input id="quantity" type="number" min="1" value="1" placeholder="الكمية" class="w-full p-3 border border-black/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-black placeholder-gray-400 pl-10">
      <i class="fa-solid fa-box-open absolute left-3 top-3 text-gray-400" aria-hidden="true"></i>
    </div>

    <!-- زر WhatsApp لتأكيد الرقم -->
    <button id="sendOtpBtn" class="w-full bg-black hover:bg-gray-800 text-white py-3 rounded-lg mb-3 font-semibold transition">
      <i class="fa-brands fa-whatsapp ml-2"></i>
      تأكيد رقم الهاتف عبر واتساب
    </button>

    <!-- قسم التأكيد -->
    <div id="otpSection" class="hidden">
      <p class="mb-3 text-center text-gray-600">
        <i class="fa-solid fa-link ml-1"></i> اضغط على الرابط وأرسل الرسالة لتأكيد رقمك:
      </p>
      <a id="whatsappLink" href="#" target="_blank" class="w-full block text-center bg-black hover:bg-gray-800 text-white py-3 rounded-lg mb-3 font-semibold transition">
        <i class="fa-regular fa-paper-plane ml-2"></i>
        افتح واتساب وأرسل الرسالة
      </a>
      <button id="confirmWhatsappBtn" class="w-full bg-black hover:bg-gray-800 text-white py-3 rounded-lg mb-3 font-semibold transition">
        <i class="fa-regular fa-circle-check ml-2"></i>
        لقد أرسلت الرسالة
      </button>
    </div>

    <div class="flex gap-3">
      <button id="sendOrder" disabled class="flex-1 bg-black text-white py-3 rounded-lg opacity-50 cursor-not-allowed font-semibold transition">
        <i class="fa-solid fa-rocket ml-2"></i>
        إرسال الطلب
      </button>
      <button id="closePopup" class="flex-1 bg-gray-200 hover:bg-gray-300 text-black py-3 rounded-lg font-semibold transition">
        <i class="fa-solid fa-xmark ml-2"></i>
        إغلاق
      </button>
    </div>
  </div>
</div>
<script type="module">
  import { initializeApp } from "https://www.gstatic.com/firebasejs/12.1.0/firebase-app.js";
  import { getFirestore, collection, addDoc } from "https://www.gstatic.com/firebasejs/12.1.0/firebase-firestore.js";

  const firebaseConfig = {
    apiKey: "AIzaSyDeFqpa8MtVF5r3kKLfYZdM3EgPZCs3zeI",
    authDomain: "zamman-admin.firebaseapp.com",
    projectId: "zamman-admin",
    storageBucket: "zamman-admin.firebasestorage.app",
    messagingSenderId: "910842307850",
    appId: "1:910842307850:web:b7548b7a3323e1a19046f3",
    measurementId: "G-9MB7VF17G4"
  };
  const app = initializeApp(firebaseConfig);
  const db = getFirestore(app);

  const popup = document.getElementById("orderPopup");
  const openBtn = document.getElementById("addToCart");
  const closeBtn = document.getElementById("closePopup");
  const sendBtn = document.getElementById("sendOrder");
  const statusMsg = document.getElementById("statusMsg");

  let confirmedWhatsapp = false;

  openBtn.addEventListener("click", () => popup.classList.remove("hidden"));
  closeBtn.addEventListener("click", () => popup.classList.add("hidden"));

  document.getElementById("sendOtpBtn").addEventListener("click", () => {
    const phone = document.getElementById("phone").value.trim();
    if (!/^[0-9]{11}$/.test(phone)) {
      statusMsg.innerHTML = '<i class="fa-solid fa-triangle-exclamation ml-1"></i> أدخل رقم مصري صحيح (11 رقم)';
      statusMsg.classList.add("text-red-500");
      return;
    }
    statusMsg.innerHTML = '<i class="fa-solid fa-circle-check ml-1"></i> الرجاء إرسال رسالة التأكيد عبر واتساب';
    statusMsg.classList.remove("text-red-500");

    const message = encodeURIComponent("مرحبًا، أريد تأكيد رقم هاتفي للطلب");
    document.getElementById("whatsappLink").href = `https://wa.me/20${phone}?text=${message}`;

    document.getElementById("otpSection").classList.remove("hidden");
  });

  document.getElementById("confirmWhatsappBtn").addEventListener("click", () => {
    confirmedWhatsapp = true;
    sendBtn.disabled = false;
    sendBtn.classList.remove("opacity-50", "cursor-not-allowed");
    statusMsg.innerHTML = '<i class="fa-solid fa-circle-check ml-1"></i> تم تأكيد الرقم، يمكنك الآن إرسال الطلب';
    statusMsg.classList.remove("text-red-500");
    statusMsg.classList.add("text-green-600");
  });

  sendBtn.addEventListener("click", async () => {
    if (!confirmedWhatsapp) {
      statusMsg.innerHTML = '<i class="fa-solid fa-triangle-exclamation ml-1"></i> يرجى تأكيد رقمك عبر واتساب أولاً';
      statusMsg.classList.add("text-red-500");
      return;
    }

    const name = document.getElementById("name").value.trim();
    const phone = document.getElementById("phone").value.trim();
    const email = document.getElementById("email").value.trim();
    const address = document.getElementById("address").value.trim();
    const quantity = parseInt(document.getElementById("quantity").value.trim()) || 1;

    if (!name || !phone || !email || !address || !quantity) {
      statusMsg.innerHTML = '<i class="fa-solid fa-triangle-exclamation ml-1"></i> يرجى ملء جميع الحقول بشكل صحيح';
      statusMsg.classList.add("text-red-500");
      return;
    }

    try {
      await addDoc(collection(db, "orders"), {
        product: "ساعة كربون أسود — أرقام عربية",
        name,
        phone: "+20" + phone,
        email,
        address,
        quantity,
        date: new Date().toISOString()
      });
      statusMsg.innerHTML = '<i class="fa-solid fa-rocket ml-1"></i> تم إرسال الطلب بنجاح!';
      statusMsg.classList.remove("text-red-500");
      statusMsg.classList.add("text-green-600");

      popup.classList.add("hidden");
      confirmedWhatsapp = false;
      sendBtn.disabled = true;
      sendBtn.classList.add("opacity-50", "cursor-not-allowed");
    } catch (error) {
      console.error("خطأ:", error);
      statusMsg.innerHTML = '<i class="fa-solid fa-circle-xmark ml-1"></i> حدث خطأ أثناء إرسال الطلب';
      statusMsg.classList.add("text-red-500");
    }
  });
</script>

      <!-- إشعار -->
      <p id="notification" class="text-black font-medium hidden">
        <i class="fa-solid fa-circle-check ml-1"></i> تم إضافة المنتج إلى السلة!
      </p>

      <!-- مواصفات -->
      <ul class="text-gray-600 space-y-2">
        <li class="flex items-center gap-2">
          <i class="fa-solid fa-circle-check text-black"></i>
          مادة الإطار: كربون
        </li>
        <li class="flex items-center gap-2">
          <i class="fa-solid fa-circle-check text-black"></i>
          نوع المينا: أرقام عربية
        </li>
        <li class="flex items-center gap-2">
          <i class="fa-solid fa-circle-check text-black"></i>
          مقاومة للماء حتى 50 متر
        </li>
        <li class="flex items-center gap-2">
          <i class="fa-solid fa-circle-check text-black"></i>
          ضمان سنة كاملة
        </li>
      </ul>

      <!-- شحن -->
      <div class="bg-gray-50 border border-black/10 rounded-lg p-4 text-sm text-gray-600 flex items-center gap-3">
        <i class="fa-solid fa-truck text-black"></i>
        <span>التوصيل خلال 2–3 أيام عمل — الدفع عند الاستلام — إمكانية الإرجاع خلال 14 يوم.</span>
      </div>
    </div>

  </main>

  <!-- فوتر -->
  <footer class="bg-white border-t border-black/10 mt-12">
    <div class="max-w-6xl mx-auto px-4 py-8">
      <div class="grid md:grid-cols-3 gap-8 mb-6">
        <div>
          <h3 class="font-bold mb-3">Zaman Elite</h3>
          <p class="text-gray-600 text-sm">متجرك الموثوق للساعات الفاخرة والإكسسوارات العصرية</p>
        </div>
        <div>
          <h4 class="font-semibold mb-3">روابط سريعة</h4>
          <ul class="space-y-2 text-sm text-gray-600">
            <li><a href="#" class="hover:text-black transition">الرئيسية</a></li>
            <li><a href="#" class="hover:text-black transition">المنتجات</a></li>
            <li><a href="#" class="hover:text-black transition">تواصل معنا</a></li>
          </ul>
        </div>
        <div>
          <h4 class="font-semibold mb-3">تواصل معنا</h4>
          <div class="space-y-2 text-sm text-gray-600">
            <div class="flex items-center gap-2">
              <i class="fa-brands fa-whatsapp"></i>
              <span>واتساب: 01234567890</span>
            </div>
            <div class="flex items-center gap-2">
              <i class="fa-regular fa-envelope"></i>
              <span><EMAIL></span>
            </div>
          </div>
        </div>
      </div>
      <div class="text-center text-sm text-gray-500 pt-6 border-t border-black/10">
        © <span id="y"></span> Zaman Elite. جميع الحقوق محفوظة.
      </div>
    </div>
  </footer>

  <!-- JavaScript -->
  <script>
    document.getElementById('y').textContent = new Date().getFullYear();

    // تغيير الصور
    const mainImage = document.getElementById('mainImage');
    document.querySelectorAll('.thumb').forEach(img => {
      img.addEventListener('click', () => {
        mainImage.src = img.src;
      });
    });

    // إضافة للسلة
    let cartCount = 0;
    const cartCountEl = document.getElementById('cartCount');
    const notification = document.getElementById('notification');

    document.getElementById('addToCart').addEventListener('click', () => {
      cartCount++;
      cartCountEl.textContent = cartCount;
      notification.classList.remove('hidden');
      setTimeout(() => notification.classList.add('hidden'), 2000);
    });
  </script>

</body>
</html>
